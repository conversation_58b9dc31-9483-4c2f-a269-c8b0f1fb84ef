# 已完成状态追加服务显示问题修复

## 问题描述

在已完成的订单详情中，状态为 `completed`（已完成）的追加服务显示"⚠️ 此服务尚未付款，无法开始计时"的错误提示。

从用户提供的截图可以看到：
- "局部去油"服务状态为 `completed`（已完成）
- 但界面显示"此服务尚未付款，无法开始计时"

## 问题分析

根据追加服务订单状态枚举：
```javascript
export enum AdditionalServiceOrderStatus {
  PENDING_CONFIRM = 'pending_confirm',  // 待确认
  CONFIRMED = 'confirmed',              // 已确认
  REJECTED = 'rejected',                // 已拒绝
  PENDING_PAYMENT = 'pending_payment',  // 待付款
  PAID = 'paid',                        // 已付款
  COMPLETED = 'completed',              // 已完成
  CANCELLED = 'cancelled',              // 已取消
  REFUNDING = 'refunding',              // 退款中
  REFUNDED = 'refunded',                // 已退款
}
```

`completed` 状态表示服务已经完成，应该被认为是已付款状态，不应该显示未付款提示。

## 问题根源

### 1. 未付款提示显示条件错误

在 `components/service-duration/service-duration.wxml` 中：
```html
<view class="unpaid-notice" wx:if="{{item.type === 'additional' && item.status !== 'paid'}}">
```

这个条件会对所有非 `paid` 状态显示未付款提示，包括 `completed` 状态。

### 2. 付款状态判断逻辑不完整

在 `pages/orders/orderDetail/index.js` 的 `isAdditionalServicePaid` 方法中：
```javascript
return service.status === 'paid';
```

只检查 `paid` 状态，没有包含 `completed` 状态。

## 解决方案

### 1. 修正未付款提示显示条件

将显示条件改为只对真正未付款的状态显示：
```html
<view class="unpaid-notice" wx:if="{{item.type === 'additional' && (item.status === 'pending_payment' || item.status === 'confirmed')}}">
```

### 2. 完善付款状态判断逻辑

修改 `isAdditionalServicePaid` 方法：
```javascript
isAdditionalServicePaid(service) {
  // 主订单增项服务默认已付款
  if (service.type === 'original') {
    return true;
  }
  // 追加服务订单需要检查状态
  // paid: 已付款, completed: 已完成 都认为是已付款状态
  return service.status === 'paid' || service.status === 'completed';
}
```

## 修改的文件

### `components/service-duration/service-duration.wxml`
- 修正未付款提示的显示条件
- 只对 `pending_payment` 和 `confirmed` 状态显示未付款提示

### `pages/orders/orderDetail/index.js`
- 修改 `isAdditionalServicePaid` 方法
- 将 `completed` 状态也认为是已付款状态

## 状态分类

修复后的状态分类：

### 已付款状态（不显示未付款提示）：
- `paid` - 已付款
- `completed` - 已完成
- `cancelled` - 已取消
- `refunding` - 退款中
- `refunded` - 已退款

### 未付款状态（显示未付款提示）：
- `pending_payment` - 待付款
- `confirmed` - 已确认（但未付款）

### 其他状态：
- `pending_confirm` - 待确认（通常在待确认列表中显示）
- `rejected` - 已拒绝（不需要付款提示）

## 预期效果

修复后：
1. `completed` 状态的追加服务不再显示未付款提示
2. 只有真正未付款的服务（`pending_payment`、`confirmed`）才显示未付款提示
3. 已完成的服务能正常显示时长信息和状态

## 测试建议

1. 测试 `completed` 状态的追加服务显示
2. 测试 `paid` 状态的追加服务显示
3. 测试 `pending_payment` 状态的追加服务显示（应该有未付款提示）
4. 测试 `confirmed` 状态的追加服务显示（应该有未付款提示）
5. 验证其他状态的正确显示
