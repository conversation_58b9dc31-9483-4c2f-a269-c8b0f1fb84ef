# 已完成订单中追加服务显示问题修复

## 问题描述

在已完成的订单详情中，服务时长部分没有显示追加服务对应的增项信息。从用户提供的截图可以看到，只显示了主服务（小体验洗护）和增项服务（全身去油），但缺少了追加服务的信息。

## 问题分析

### 原始逻辑问题
在 `loadAllAdditionalServices` 方法中，追加服务的 `needDurationTracking` 属性设置为：
```javascript
needDurationTracking: item.status === 'paid', // 只有已付款的才需要时长统计
```

这个逻辑对于服务中的订单是正确的，但对于已完成的订单来说，我们应该显示所有已确认的追加服务的时长信息，不管它们的付款状态如何。

### 显示条件分析
服务时长组件的模板中：
1. 增项服务项的显示条件：`wx:if="{{item.needDurationTracking}}"`
2. 操作按钮的显示条件：`wx:if="{{item.showDurationActions}}"`

如果 `needDurationTracking` 为 `false`，整个增项服务项都不会显示。

## 解决方案

### 1. 修改 needDurationTracking 逻辑

根据订单状态调整 `needDurationTracking` 的判断逻辑：

```javascript
// 根据订单状态决定是否需要时长统计
const orderStatus = this.data.orderDetail?.status;
let needDurationTracking = false;

if (orderStatus === '服务中') {
  // 服务中状态：只有已付款的才需要时长统计
  needDurationTracking = item.status === 'paid';
} else if (['已完成', '已评价'].includes(orderStatus)) {
  // 已完成状态：所有已确认的追加服务都显示时长信息
  needDurationTracking = ['confirmed', 'paid', 'completed'].includes(item.status);
} else {
  // 其他状态：按原逻辑处理
  needDurationTracking = item.status === 'paid';
}
```

### 2. 添加调试信息

为了便于排查问题，添加了详细的调试日志：

1. **订单详情页面**：
   - 显示订单状态
   - 显示增项服务分类结果
   - 显示传递给服务时长组件的数据

2. **服务时长组件**：
   - 添加数据观察器，监控接收到的数据
   - 在模板中添加调试信息显示

### 3. 修改的文件

#### `pages/orders/orderDetail/index.js`
- 修改 `loadAllAdditionalServices` 方法中的 `needDurationTracking` 逻辑
- 添加详细的调试日志

#### `components/service-duration/service-duration.js`
- 添加数据观察器监控 `allAdditionalServices` 变化

#### `components/service-duration/service-duration.wxml`
- 添加调试信息显示

## 预期效果

修复后，对于已完成的订单：

1. **主订单增项服务**：始终显示（`needDurationTracking: true`）
2. **已确认的追加服务**：显示时长信息（状态为 `confirmed`、`paid`、`completed`）
3. **操作按钮**：在已完成订单中不显示（符合预期）

## 测试建议

1. 测试已完成订单中的追加服务显示
2. 测试服务中订单的追加服务显示（确保不影响原有逻辑）
3. 检查调试日志确认数据传递正确
4. 验证不同状态的追加服务显示逻辑

## 调试步骤

如果问题仍然存在，可以通过以下步骤排查：

1. 查看控制台中的调试日志：
   - "🔍 增项服务分类结果"
   - "🔍 传递给服务时长组件的数据"
   - "🔍 服务时长组件接收到的 allAdditionalServices 数据"

2. 检查服务时长组件模板中的调试信息显示

3. 确认追加服务的状态是否符合预期（应该是 `confirmed`、`paid` 或 `completed`）

4. 验证 `needDurationTracking` 属性是否正确设置为 `true`
